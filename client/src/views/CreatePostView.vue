<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- 页面标题 -->
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-gray-900">发布走失信息</h1>
        <p class="mt-2 text-gray-600">请详细填写宠物信息，帮助更多人识别您的宠物</p>
      </div>

      <!-- 步骤指示器 -->
      <div class="mb-8">
        <nav aria-label="Progress">
          <ol class="flex items-center justify-center space-x-5">
            <li v-for="(step, index) in steps" :key="step.id" class="flex items-center">
              <div
                class="flex items-center justify-center w-8 h-8 rounded-full border-2 text-sm font-medium"
                :class="getStepClass(index)"
              >
                {{ index + 1 }}
              </div>
              <span class="ml-2 text-sm font-medium" :class="getStepTextClass(index)">
                {{ step.name }}
              </span>
              <div v-if="index < steps.length - 1" class="ml-5 w-5 h-px bg-gray-300"></div>
            </li>
          </ol>
        </nav>
      </div>

      <!-- 表单内容 -->
      <div class="bg-white shadow rounded-lg">
        <form @submit.prevent="handleSubmit">
          <!-- 步骤1: 宠物基本信息 -->
          <div v-show="currentStep === 0" class="p-6 space-y-6">
            <h2 class="text-lg font-medium text-gray-900 mb-4">宠物基本信息</h2>
            
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <!-- 宠物名字 -->
              <div>
                <label for="petName" class="block text-sm font-medium text-gray-700">
                  宠物名字 <span class="text-red-500">*</span>
                </label>
                <input
                  id="petName"
                  v-model="petForm.name"
                  type="text"
                  required
                  class="mt-1 input-field"
                  placeholder="请输入宠物名字"
                />
              </div>

              <!-- 宠物品种 -->
              <div>
                <label for="petSpecies" class="block text-sm font-medium text-gray-700">
                  宠物品种 <span class="text-red-500">*</span>
                </label>
                <select
                  id="petSpecies"
                  v-model="petForm.species"
                  required
                  class="mt-1 input-field"
                >
                  <option value="">请选择品种</option>
                  <option v-for="species in PET_SPECIES" :key="species.value" :value="species.value">
                    {{ species.label }}
                  </option>
                </select>
              </div>

              <!-- 品种详情 -->
              <div>
                <label for="petBreed" class="block text-sm font-medium text-gray-700">
                  具体品种（可选）
                </label>
                <input
                  id="petBreed"
                  v-model="petForm.breed"
                  type="text"
                  class="mt-1 input-field"
                  placeholder="如：金毛、波斯猫等"
                />
              </div>

              <!-- 主要毛色 -->
              <div>
                <label for="petColor" class="block text-sm font-medium text-gray-700">
                  主要毛色 <span class="text-red-500">*</span>
                </label>
                <select
                  id="petColor"
                  v-model="petForm.color"
                  required
                  class="mt-1 input-field"
                >
                  <option value="">请选择毛色</option>
                  <option v-for="color in PET_COLORS" :key="color.value" :value="color.value">
                    {{ color.label }}
                  </option>
                </select>
              </div>

              <!-- 性别 -->
              <div>
                <label for="petGender" class="block text-sm font-medium text-gray-700">
                  性别 <span class="text-red-500">*</span>
                </label>
                <select
                  id="petGender"
                  v-model="petForm.gender"
                  required
                  class="mt-1 input-field"
                >
                  <option value="">请选择性别</option>
                  <option v-for="gender in PET_GENDERS" :key="gender.value" :value="gender.value">
                    {{ gender.label }}
                  </option>
                </select>
              </div>

              <!-- 年龄 -->
              <div>
                <label for="petAge" class="block text-sm font-medium text-gray-700">
                  年龄（可选）
                </label>
                <input
                  id="petAge"
                  v-model.number="petForm.age"
                  type="number"
                  min="0"
                  max="30"
                  class="mt-1 input-field"
                  placeholder="请输入年龄"
                />
              </div>
            </div>

            <!-- 宠物描述 -->
            <div>
              <label for="petDescription" class="block text-sm font-medium text-gray-700">
                外观特征描述（可选）
              </label>
              <textarea
                id="petDescription"
                v-model="petForm.description"
                rows="3"
                class="mt-1 input-field"
                placeholder="请描述宠物的外观特征，如体型、特殊标记等"
              ></textarea>
            </div>

            <!-- 宠物照片 -->
            <ImageUpload
              v-model="petForm.photo"
              label="宠物照片"
              :required="true"
              help-text="请上传清晰的宠物照片，有助于他人识别"
            />
          </div>

          <!-- 步骤2: 走失信息 -->
          <div v-show="currentStep === 1" class="p-6 space-y-6">
            <h2 class="text-lg font-medium text-gray-900 mb-4">走失信息</h2>
            
            <!-- 走失时间 -->
            <div>
              <label for="lostTime" class="block text-sm font-medium text-gray-700">
                走失时间 <span class="text-red-500">*</span>
              </label>
              <input
                id="lostTime"
                v-model="postForm.last_seen_time"
                type="datetime-local"
                required
                class="mt-1 input-field"
              />
            </div>

            <!-- 走失地点 -->
            <MapPicker
              v-model="selectedLocation"
              label="走失地点"
              placeholder="请输入走失地点进行搜索"
              :required="true"
            />

            <!-- 联系方式 -->
            <div>
              <label for="contactInfo" class="block text-sm font-medium text-gray-700">
                联系方式（可选）
              </label>
              <textarea
                id="contactInfo"
                v-model="postForm.contact_info"
                rows="2"
                class="mt-1 input-field"
                placeholder="如需要，可以提供额外的联系方式"
              ></textarea>
              <p class="mt-1 text-sm text-gray-500">
                系统会自动使用您的注册邮箱作为联系方式，此处可提供额外联系方式
              </p>
            </div>

            <!-- 视频链接 -->
            <div>
              <label for="videoUrl" class="block text-sm font-medium text-gray-700">
                视频链接（可选）
              </label>
              <input
                id="videoUrl"
                v-model="postForm.video_url"
                type="url"
                class="mt-1 input-field"
                placeholder="如有宠物视频，请提供链接"
              />
            </div>
          </div>

          <!-- 错误提示 -->
          <div v-if="error" class="p-6 pt-0">
            <div class="rounded-md bg-red-50 p-4">
              <div class="flex">
                <div class="flex-shrink-0">
                  <ExclamationTriangleIcon class="h-5 w-5 text-red-400" />
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-red-800">
                    {{ error }}
                  </h3>
                </div>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="px-6 py-4 bg-gray-50 flex justify-between">
            <button
              v-if="currentStep > 0"
              type="button"
              @click="previousStep"
              class="btn-secondary"
            >
              上一步
            </button>
            <div v-else></div>

            <div class="flex space-x-3">
              <router-link
                to="/"
                class="btn-secondary"
              >
                取消
              </router-link>
              
              <button
                v-if="currentStep < steps.length - 1"
                type="button"
                @click="nextStep"
                :disabled="!canProceedToNext"
                class="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                下一步
              </button>
              
              <button
                v-else
                type="submit"
                :disabled="loading || !canSubmit"
                class="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span v-if="loading" class="flex items-center">
                  <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  发布中...
                </span>
                <span v-else>发布信息</span>
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ExclamationTriangleIcon } from '@heroicons/vue/24/outline'
import { useAuthStore } from '@/stores/auth'
import { petService } from '@/services/pets'
import { postService } from '@/services/posts'
import ImageUpload from '@/components/ImageUpload.vue'
import MapPicker from '@/components/MapPicker.vue'
import type { PetFormData, PostFormData, MapLocation } from '@/types'
import { PET_SPECIES, PET_COLORS, PET_GENDERS } from '@/constants'

const router = useRouter()
const authStore = useAuthStore()

// 步骤定义
const steps = [
  { id: 1, name: '宠物信息' },
  { id: 2, name: '走失信息' },
]

const currentStep = ref(0)
const loading = ref(false)
const error = ref('')

// 表单数据
const petForm = ref<PetFormData>({
  name: '',
  species: '',
  breed: '',
  color: '',
  gender: 'unknown',
  age: undefined,
  description: '',
  photo: null,
})

const postForm = ref<PostFormData>({
  pet_id: 0,
  last_seen_location: '',
  last_seen_time: '',
  video_url: '',
  contact_info: '',
})

const selectedLocation = ref<MapLocation | null>(null)

// 计算属性
const getStepClass = (index: number) => {
  if (index < currentStep.value) {
    return 'bg-primary-600 border-primary-600 text-white'
  } else if (index === currentStep.value) {
    return 'bg-white border-primary-600 text-primary-600'
  } else {
    return 'bg-white border-gray-300 text-gray-500'
  }
}

const getStepTextClass = (index: number) => {
  if (index <= currentStep.value) {
    return 'text-primary-600'
  } else {
    return 'text-gray-500'
  }
}

const canProceedToNext = computed(() => {
  if (currentStep.value === 0) {
    return petForm.value.name && 
           petForm.value.species && 
           petForm.value.color && 
           petForm.value.gender &&
           petForm.value.photo
  }
  return true
})

const canSubmit = computed(() => {
  return canProceedToNext.value && 
         postForm.value.last_seen_time && 
         selectedLocation.value
})

// 方法
const nextStep = () => {
  if (canProceedToNext.value && currentStep.value < steps.length - 1) {
    currentStep.value++
  }
}

const previousStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const handleSubmit = async () => {
  if (!canSubmit.value) return
  
  try {
    loading.value = true
    error.value = ''
    
    // 首先创建宠物信息
    const petResponse = await petService.create(petForm.value)
    
    if (!petResponse.success || !petResponse.data) {
      throw new Error(petResponse.message || '创建宠物信息失败')
    }
    
    // 然后创建帖子
    const postData: PostFormData = {
      pet_id: petResponse.data.id,
      last_seen_location: selectedLocation.value?.address || `${selectedLocation.value?.lat}, ${selectedLocation.value?.lng}`,
      last_seen_time: postForm.value.last_seen_time,
      video_url: postForm.value.video_url || undefined,
      contact_info: postForm.value.contact_info || undefined,
    }
    
    const postResponse = await postService.create(postData)
    
    if (!postResponse.success) {
      throw new Error(postResponse.message || '创建帖子失败')
    }
    
    // 成功后跳转到帖子详情页或我的帖子页
    router.push('/dashboard/posts')
    
  } catch (err: any) {
    console.error('发布失败:', err)
    error.value = err.message || '发布失败，请稍后重试'
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  // 检查用户是否已登录
  if (!authStore.isAuthenticated) {
    router.push('/login')
  }
})
</script>
