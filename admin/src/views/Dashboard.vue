<template>
  <div class="dashboard">
    <h2>数据统计面板</h2>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon posts">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ postStats?.total_posts || 0 }}</h3>
              <p>总帖子数</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon pets">
              <el-icon><Sunny /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ petStats.total_pets }}</h3>
              <p>总宠物数</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon sightings">
              <el-icon><View /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ sightingStats.total_sightings }}</h3>
              <p>总线索数</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon pending">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ postStats.pending_posts }}</h3>
              <p>待审核帖子</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细统计 -->
    <el-row :gutter="20" class="detail-stats">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>帖子状态统计</span>
          </template>
          <div class="stat-list">
            <div class="stat-item">
              <span>寻找中</span>
              <el-tag type="warning">{{ postStats.searching_posts }}</el-tag>
            </div>
            <div class="stat-item">
              <span>已找到</span>
              <el-tag type="success">{{ postStats.found_posts }}</el-tag>
            </div>
            <div class="stat-item">
              <span>已审核通过</span>
              <el-tag type="primary">{{ postStats.approved_posts }}</el-tag>
            </div>
            <div class="stat-item">
              <span>审核被拒</span>
              <el-tag type="danger">{{ postStats.rejected_posts }}</el-tag>
            </div>
            <div class="stat-item">
              <span>今日新增</span>
              <el-tag>{{ postStats.today_posts }}</el-tag>
            </div>
            <div class="stat-item">
              <span>本周新增</span>
              <el-tag>{{ postStats.week_posts }}</el-tag>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card>
          <template #header>
            <span>宠物类型统计</span>
          </template>
          <div class="stat-list">
            <div class="stat-item">
              <span>狗狗</span>
              <el-tag type="warning">{{ petStats.dogs }}</el-tag>
            </div>
            <div class="stat-item">
              <span>猫咪</span>
              <el-tag type="primary">{{ petStats.cats }}</el-tag>
            </div>
            <div class="stat-item">
              <span>雄性</span>
              <el-tag>{{ petStats.males }}</el-tag>
            </div>
            <div class="stat-item">
              <span>雌性</span>
              <el-tag>{{ petStats.females }}</el-tag>
            </div>
            <div class="stat-item">
              <span>今日新增</span>
              <el-tag type="success">{{ petStats.today_added }}</el-tag>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 线索统计 -->
    <el-row :gutter="20" class="detail-stats">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>线索统计</span>
          </template>
          <div class="stat-list">
            <div class="stat-item">
              <span>已验证线索</span>
              <el-tag type="success">{{ sightingStats.verified_sightings }}</el-tag>
            </div>
            <div class="stat-item">
              <span>未验证线索</span>
              <el-tag type="warning">{{ sightingStats.unverified_sightings }}</el-tag>
            </div>
            <div class="stat-item">
              <span>带照片线索</span>
              <el-tag type="primary">{{ sightingStats.sightings_with_photo }}</el-tag>
            </div>
            <div class="stat-item">
              <span>今日新增</span>
              <el-tag>{{ sightingStats.today_sightings }}</el-tag>
            </div>
            <div class="stat-item">
              <span>本周新增</span>
              <el-tag>{{ sightingStats.week_sightings }}</el-tag>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card>
          <template #header>
            <span>快速操作</span>
          </template>
          <div class="quick-actions">
            <el-button type="primary" @click="$router.push('/posts/pending')">
              <el-icon><Warning /></el-icon>
              审核帖子 ({{ postStats.pending_posts }})
            </el-button>
            <el-button type="success" @click="$router.push('/posts')">
              <el-icon><Document /></el-icon>
              管理帖子
            </el-button>
            <el-button type="info" @click="$router.push('/sightings')">
              <el-icon><View /></el-icon>
              查看线索
            </el-button>
            <el-button :loading="loading" @click="refreshStats">
              <el-icon><Refresh /></el-icon>
              刷新数据
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Document,
  Sunny,
  View,
  Warning,
  Refresh
} from '@element-plus/icons-vue'
import { getPostStats } from '@/api/posts'
import { getPetStats } from '@/api/pets'
import { getSightingStats } from '@/api/sightings'
import type { PostStats } from '@/api/posts'
import type { PetStats } from '@/api/pets'
import type { SightingStats } from '@/api/sightings'

const postStats = ref<PostStats>({
  total_posts: 0,
  searching_posts: 0,
  found_posts: 0,
  pending_posts: 0,
  approved_posts: 0,
  rejected_posts: 0,
  today_posts: 0,
  week_posts: 0
})

const petStats = ref<PetStats>({
  total_pets: 0,
  dogs: 0,
  cats: 0,
  males: 0,
  females: 0,
  today_added: 0
})

const sightingStats = ref<SightingStats>({
  total_sightings: 0,
  verified_sightings: 0,
  unverified_sightings: 0,
  today_sightings: 0,
  week_sightings: 0,
  sightings_with_photo: 0
})

const loading = ref(false)

// 获取统计数据
const fetchStats = async () => {
  loading.value = true

  try {
    const [postResponse, petResponse, sightingResponse] = await Promise.all([
      getPostStats(),
      getPetStats(),
      getSightingStats()
    ])

    postStats.value = postResponse
    petStats.value = petResponse
    sightingStats.value = sightingResponse

  } catch (error) {
    console.error('获取统计数据失败:', error)
    ElMessage.error('获取统计数据失败')
  } finally {
    loading.value = false
  }
}

// 刷新统计数据
const refreshStats = async () => {
  await fetchStats()
  ElMessage.success('数据已刷新')
}

onMounted(() => {
  fetchStats()
})
</script>

<style scoped>
.dashboard h2 {
  margin-bottom: 20px;
  color: #333;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.stat-icon.posts {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.pets {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.sightings {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.pending {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info h3 {
  margin: 0 0 5px 0;
  font-size: 28px;
  font-weight: bold;
  color: #333;
}

.stat-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.detail-stats {
  margin-bottom: 20px;
}

.stat-list {
  padding: 10px 0;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.stat-item:last-child {
  border-bottom: none;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 10px 0;
}

.quick-actions .el-button {
  justify-content: flex-start;
}
</style>
